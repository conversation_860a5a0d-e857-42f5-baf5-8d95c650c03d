{"name": "saloncopilot", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "expo": "~53.0.9", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "18.2.0", "react-native": "0.74.5", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-preset-expo": "~13.1.11", "tailwindcss": "^3.4.17"}, "private": true}
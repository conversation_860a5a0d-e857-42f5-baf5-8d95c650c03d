{"name": "saloncopilot", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.9", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.2", "react-native-reanimated": "~3.16.1"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-preset-expo": "~13.1.11", "tailwindcss": "^3.4.17"}, "private": true}
import { View, Text } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  return (
    <View style={{ flex: 1, backgroundColor: '#f5f5f5', alignItems: 'center', justifyContent: 'center', padding: 20 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#333', textAlign: 'center', marginBottom: 10 }}>
        ¡Salon Copilot funcionando!
      </Text>
      <Text style={{ fontSize: 16, color: '#666', textAlign: 'center', marginBottom: 20 }}>
        La HomeScreen se configurará en el siguiente paso
      </Text>
      <Text style={{ fontSize: 14, color: '#888', textAlign: 'center' }}>
        📱 Aplicación base funcionando correctamente
      </Text>
      <StatusBar style="auto" />
    </View>
  );
}

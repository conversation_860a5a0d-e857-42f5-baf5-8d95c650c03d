{"name": "saloncopilot", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "expo": "~53.0.9", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.10.0"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-preset-expo": "~13.1.11", "tailwindcss": "^3.4.17"}, "private": true}
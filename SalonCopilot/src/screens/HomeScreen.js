import React from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function HomeScreen() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>
            ¡Bienvenido a Salon Copilot!
          </Text>
          <Text style={styles.subtitle}>
            Tu asistente para la gestión del salón
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Acciones Rápidas
          </Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity style={[styles.button, styles.primaryButton]}>
              <Text style={styles.buttonText}>Nueva Cita</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.secondaryButton]}>
              <Text style={styles.buttonText}>Ver Agenda</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Dashboard Cards */}
        <View style={styles.cardsContainer}>
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Citas de Hoy</Text>
            <Text style={styles.cardNumber}>12</Text>
            <Text style={styles.cardSubtext}>3 pendientes, 9 completadas</Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.cardTitle}>Ingresos del Día</Text>
            <Text style={styles.cardNumber}>€450</Text>
            <Text style={styles.cardSubtext}>+15% respecto a ayer</Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.cardTitle}>Próximas Citas</Text>
            <View style={styles.appointmentItem}>
              <View>
                <Text style={styles.appointmentName}>María García</Text>
                <Text style={styles.appointmentService}>Corte y peinado</Text>
              </View>
              <Text style={styles.appointmentTime}>14:30</Text>
            </View>
            <View style={styles.appointmentItem}>
              <View>
                <Text style={styles.appointmentName}>Ana López</Text>
                <Text style={styles.appointmentService}>Tinte y mechas</Text>
              </View>
              <Text style={styles.appointmentTime}>16:00</Text>
            </View>
          </View>
        </View>
      </View>
      <StatusBar style="auto" />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fafafa',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#171717',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#525252',
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#262626',
    marginBottom: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#ec4899',
  },
  secondaryButton: {
    backgroundColor: '#64748b',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  cardsContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: 'white',
    padding: 24,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#262626',
    marginBottom: 12,
  },
  cardNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ec4899',
    marginBottom: 8,
  },
  cardSubtext: {
    fontSize: 14,
    color: '#525252',
  },
  appointmentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  appointmentName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#262626',
  },
  appointmentService: {
    fontSize: 14,
    color: '#525252',
  },
  appointmentTime: {
    fontSize: 14,
    fontWeight: '500',
    color: '#ec4899',
  },
});
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function HomeScreen() {
  return (
    <ScrollView className="flex-1 bg-neutral-50">
      <View className="flex-1 px-4 py-8">
        {/* Header */}
        <View className="mb-8">
          <Text className="text-3xl font-bold text-neutral-900 mb-2">
            ¡Bienvenido a Salon Copilot!
          </Text>
          <Text className="text-lg text-neutral-600">
            Tu asistente para la gestión del salón
          </Text>
        </View>

        {/* Quick Actions */}
        <View className="mb-8">
          <Text className="text-xl font-semibold text-neutral-800 mb-4">
            Acciones Rápidas
          </Text>
          <View className="flex-row gap-4">
            <TouchableOpacity className="flex-1 bg-primary-500 py-4 px-6 rounded-xl items-center shadow-soft">
              <Text className="text-white font-semibold text-base">Nueva Cita</Text>
            </TouchableOpacity>
            <TouchableOpacity className="flex-1 bg-secondary-500 py-4 px-6 rounded-xl items-center shadow-soft">
              <Text className="text-white font-semibold text-base">Ver Agenda</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Dashboard Cards */}
        <View className="gap-4">
          <View className="bg-white p-6 rounded-2xl shadow-soft">
            <Text className="text-lg font-semibold text-neutral-800 mb-3">Citas de Hoy</Text>
            <Text className="text-3xl font-bold text-primary-500 mb-2">12</Text>
            <Text className="text-sm text-neutral-600">3 pendientes, 9 completadas</Text>
          </View>

          <View className="bg-white p-6 rounded-2xl shadow-soft">
            <Text className="text-lg font-semibold text-neutral-800 mb-3">Ingresos del Día</Text>
            <Text className="text-3xl font-bold text-primary-500 mb-2">€450</Text>
            <Text className="text-sm text-neutral-600">+15% respecto a ayer</Text>
          </View>

          <View className="bg-white p-6 rounded-2xl shadow-soft">
            <Text className="text-lg font-semibold text-neutral-800 mb-3">Próximas Citas</Text>
            <View className="flex-row justify-between items-center mb-3">
              <View>
                <Text className="text-base font-medium text-neutral-800">María García</Text>
                <Text className="text-sm text-neutral-600">Corte y peinado</Text>
              </View>
              <Text className="text-sm font-medium text-primary-500">14:30</Text>
            </View>
            <View className="flex-row justify-between items-center">
              <View>
                <Text className="text-base font-medium text-neutral-800">Ana López</Text>
                <Text className="text-sm text-neutral-600">Tinte y mechas</Text>
              </View>
              <Text className="text-sm font-medium text-primary-500">16:00</Text>
            </View>
          </View>
        </View>
      </View>
      <StatusBar style="auto" />
    </ScrollView>
  );
}
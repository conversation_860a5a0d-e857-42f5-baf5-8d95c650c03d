import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fafafa',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#171717',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#525252',
    marginBottom: 32,
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 32,
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#ec4899',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: '#64748b',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16,
  },
  card: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#262626',
    marginBottom: 8,
  },
  cardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ec4899',
    marginBottom: 4,
  },
  cardSubtext: {
    fontSize: 14,
    color: '#525252',
  },
});

export default function App() {
  return (
    <View style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>¡Salon Copilot!</Text>
        <Text style={styles.subtitle}>Tu asistente para la gestión del salón</Text>

        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.primaryButton}>
            <Text style={styles.buttonText}>Nueva Cita</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.secondaryButton}>
            <Text style={styles.buttonText}>Ver Agenda</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>Citas de Hoy</Text>
          <Text style={styles.cardValue}>12</Text>
          <Text style={styles.cardSubtext}>3 pendientes, 9 completadas</Text>
        </View>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>Ingresos del Día</Text>
          <Text style={styles.cardValue}>€450</Text>
          <Text style={styles.cardSubtext}>+15% respecto a ayer</Text>
        </View>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>Próximas Citas</Text>
          <Text style={styles.cardSubtext}>María García - 14:30</Text>
          <Text style={styles.cardSubtext}>Ana López - 16:00</Text>
        </View>
      </ScrollView>
      <StatusBar style="auto" />
    </View>
  );
}

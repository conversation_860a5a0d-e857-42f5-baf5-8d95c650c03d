import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

export default function HomeScreen() {
  return (
    <ScrollView className="flex-1 bg-neutral-50">
      <View className="flex-1 px-4 py-8">
        {/* Header */}
        <View className="mb-8">
          <Text className="text-3xl font-bold text-neutral-900 mb-2">
            ¡Bienvenido a Salon Copilot!
          </Text>
          <Text className="text-lg text-neutral-600">
            Tu asistente para la gestión del salón
          </Text>
        </View>

        {/* Quick Actions */}
        <View className="mb-8">
          <Text className="text-xl font-semibold text-neutral-800 mb-4">
            Acciones Rápidas
          </Text>
          <View className="flex-row gap-4">
            <View className="flex-1">
              <Button variant="primary" size="lg">
                Nueva Cita
              </Button>
            </View>
            <View className="flex-1">
              <Button variant="secondary" size="lg">
                Ver Agenda
              </Button>
            </View>
          </View>
        </View>

        {/* Dashboard Cards */}
        <View className="space-y-4">
          <Card variant="elevated" padding="lg">
            <Card.Header>
              <Text className="text-lg font-semibold text-neutral-800">
                Citas de Hoy
              </Text>
            </Card.Header>
            <Card.Content>
              <Text className="text-3xl font-bold text-primary-600 mb-2">
                12
              </Text>
              <Text className="text-sm text-neutral-600">
                3 pendientes, 9 completadas
              </Text>
            </Card.Content>
          </Card>

          <Card variant="elevated" padding="lg">
            <Card.Header>
              <Text className="text-lg font-semibold text-neutral-800">
                Ingresos del Día
              </Text>
            </Card.Header>
            <Card.Content>
              <Text className="text-3xl font-bold text-accent-600 mb-2">
                €450
              </Text>
              <Text className="text-sm text-neutral-600">
                +15% respecto a ayer
              </Text>
            </Card.Content>
          </Card>

          <Card variant="outlined" padding="lg">
            <Card.Header>
              <Text className="text-lg font-semibold text-neutral-800">
                Próximas Citas
              </Text>
            </Card.Header>
            <Card.Content>
              <View className="space-y-3">
                <View className="flex-row justify-between items-center">
                  <View>
                    <Text className="font-medium text-neutral-800">
                      María García
                    </Text>
                    <Text className="text-sm text-neutral-600">
                      Corte y peinado
                    </Text>
                  </View>
                  <Text className="text-sm font-medium text-primary-600">
                    14:30
                  </Text>
                </View>
                <View className="flex-row justify-between items-center">
                  <View>
                    <Text className="font-medium text-neutral-800">
                      Ana López
                    </Text>
                    <Text className="text-sm text-neutral-600">
                      Tinte y mechas
                    </Text>
                  </View>
                  <Text className="text-sm font-medium text-primary-600">
                    16:00
                  </Text>
                </View>
              </View>
            </Card.Content>
            <Card.Footer>
              <Button variant="ghost" size="sm">
                Ver todas las citas
              </Button>
            </Card.Footer>
          </Card>
        </View>
      </View>
      <StatusBar style="auto" />
    </ScrollView>
  );
}